package com.amobilab.ezmath.ai.presentation.ui.chat_bot


import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.common.utils.dlog
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.extentions.appClickable
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppBoxCentered
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppColumnCentered
import amobi.module.compose.foundation.AppDivider
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppRowCentered
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import amobi.module.compose.theme.AppThemeWrapper
import android.annotation.SuppressLint
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.models.ChatUiEvent
import com.amobilab.ezmath.ai.data.models.Message
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_components.BottomBarFreeTrialInfo
import com.amobilab.ezmath.ai.presentation.common.shared_components.BottomBarInput
import com.amobilab.ezmath.ai.presentation.common.shared_components.MessageAdapter
import com.amobilab.ezmath.ai.presentation.common.shared_components.ModelAiModeBottomSheet
import com.amobilab.ezmath.ai.presentation.common.shared_components.ModelTooltip
import com.amobilab.ezmath.ai.presentation.common.shared_components.SuggestionRow
import com.amobilab.ezmath.ai.presentation.common.shared_components.chatPreviewBitmapFromUri
import com.amobilab.ezmath.ai.presentation.common.shared_values.BotType
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainDataViewModel
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import com.skydoves.balloon.ArrowOrientation
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.BalloonAnimation
import com.skydoves.balloon.compose.Balloon
import com.skydoves.balloon.compose.BalloonWindow
import com.skydoves.balloon.compose.rememberBalloonBuilder
import com.skydoves.balloon.compose.setBackgroundColor
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import java.util.Locale
import kotlin.math.roundToLong
import com.amobilab.ezmath.ai.presentation.common.shared_components.SuggestionEvent
import com.amobilab.ezmath.ai.utils.StringUtils

@AppPreview
@Composable
fun ChatBotComposePreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        ChatBotCompose("", "", "", ChatQuestionMode.Math)
    }
}

@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun ChatBotCompose(
    idHistory: String,
    ask: String,
    urlBitmap: String,
    mode: ChatQuestionMode,
) {

    val mainDataViewModel = hiltViewModel<MainDataViewModel>()

    val navigatorViewModel = NavigatorViewModel.getInstance()


    val context = LocalContext.current


    mainDataViewModel.modeScan.value = mode

    // State to hold the selected image URI
    val uriState = remember { MutableStateFlow("") }

    val showLoading = remember { mutableStateOf(false) }


    val bitmap = chatPreviewBitmapFromUri(uriState)
    val coroutineScope = rememberCoroutineScope()

    // Sử dụng rememberSaveable để giữ giá trị khi quay lại
    val savedAsk = rememberSaveable { mutableStateOf(ask) }
    val savedUrlBitmap = rememberSaveable { mutableStateOf(urlBitmap) }
    val savedIdHistory = rememberSaveable { mutableStateOf(idHistory) }

    LaunchedEffect(savedAsk.value, savedUrlBitmap.value, savedIdHistory.value) {
        if (savedIdHistory.value.isNotEmpty()) {
            mainDataViewModel.loadChatsForHistory(savedIdHistory.value)
            showLoading.value = true
        } else if (savedUrlBitmap.value.isNotEmpty()) {
            val getBitmap = mainDataViewModel.getBitmapFromFile(savedUrlBitmap.value)

            mainDataViewModel.onEvent(
                context,
                ChatUiEvent.SendPrompt(
                    savedAsk.value.ifEmpty { context.getString(mode.promptImageId) },
                    getBitmap
                )
            )
        } else if (
            savedAsk.value.isNotEmpty()
        ) {
            mainDataViewModel.onEvent(
                context,
                ChatUiEvent.SendPrompt(
                    savedAsk.value.ifEmpty { context.getString(mode.promptImageId) },
                    bitmap
                )
            )
        }

        savedAsk.value = ""
        savedUrlBitmap.value = ""
        savedIdHistory.value = ""
    }


    // Tự động cuộn đến cuối khi danh sách chat thay đổi
    val scrollState = rememberScrollState()
    LaunchedEffect(scrollState.maxValue) {
        scrollState.animateScrollTo(
            value = scrollState.maxValue,
            animationSpec = tween(500)
        )
    }

    //====================================================================================================

    val chatState = mainDataViewModel.chatState.collectAsState().value

    val recyclerView = remember { RecyclerView(context) }

    // Tạo danh sách tin nhắn từ trạng thái chat
    val messages = remember(chatState.chatList) {
        chatState.chatList.reversed().map { chat ->
            Message(
                image = chat.bitmap,
                message = chat.prompt,
                isError = chat.isError,
                sentBy = when {
                    chat.isFromUser -> Message.SENT_BY_ME
                    chat.botType == BotType.BOT_GEMINI -> Message.SENT_BY_BOT_GEMINI
                    chat.botType == BotType.BOT_GPT -> Message.SENT_BY_BOT_GPT
                    else -> Message.SENT_BY_BOT
                }
            )
        }.toMutableList()
    }

    // Tạo adapter cho RecyclerView
    val textLeftColor = AppColors.current.textLeftChat
    val textRightColor = AppColors.current.textRightChat
    val backgroundLeftChat = AppColors.current.backgroundLeftChat
    val backgroundRightChat = AppColors.current.backgroundRightChat
    val messageAdapter = remember {
        MessageAdapter(
            messages,
            backgroundLeftChat = backgroundLeftChat,
            backgroundRightChat = backgroundRightChat,
            textLeftColor = textLeftColor,
            textRightColor = textRightColor,
            onResendMessage = { message ->
                // Handle the resend action here
                mainDataViewModel.onEvent(
                    context,
                    ChatUiEvent.ResendMessage(
                        message.message,
                        message.image
                    )
                )
                // Không cần cập nhật suggestionPrompt vì nó sẽ được cập nhật từ MainViewModel
            },
            currentMode = mode
        )
    }

    // Biến lưu kích thước danh sách trước đó
    var previousChatListSize by remember { mutableIntStateOf(chatState.chatList.size) }

    // Khi thêm hoặc cập nhật item
    LaunchedEffect(chatState.chatList) {
        if (messages.size != previousChatListSize) {
            val lastItemPosition = messages.size - 1
            messageAdapter.updateMessages(messages) // Cập nhật danh sách tin nhắn
            recyclerView.scrollToPosition(lastItemPosition) // Cuộn đến vị trí cuối
            previousChatListSize = messages.size // Cập nhật kích thước danh sách
        } else if (messages.isNotEmpty()) {
            // Cập nhật tin nhắn cuối cùng nếu không có tin nhắn mới
            messageAdapter.updateLastChat(messages.last())
            recyclerView.scrollToPosition(messages.size - 1)
        }
    }

    val showBottomSheetSelectModel = remember { mutableStateOf(false) }

    var selectedModelIcon by remember {
        mutableIntStateOf(
            when (PrefAssist.getString(PrefConst.MODEL_AI)) {
                ModelAiMode.GEMINI.name -> R.drawable.ic_gemini
                ModelAiMode.GPT.name -> R.drawable.ic_chat_gpt
                else -> R.drawable.ic_chat_gpt
            }
        )
    }
    // Lấy lastPromptToSend từ MainViewModel khi không đủ coin
    val lastPromptToSend = mainDataViewModel.lastPromptToSend.collectAsState().value

    // Khởi tạo suggestionPrompt với giá trị từ lastPromptToSend hoặc rỗng
    var suggestionEvent by remember(lastPromptToSend) { mutableStateOf(SuggestionEvent(lastPromptToSend)) }

    var showSuggestion by remember { mutableStateOf(true) }

    // Khởi tạo BalloonWindow
    var balloonWindow: BalloonWindow? by remember { mutableStateOf(null) }

    val builder = rememberBalloonBuilder {
        setArrowSize(10)
        setArrowPosition(0.5f)
        setMarginLeft(70)
        setMarginRight(16)
        setMarginVertical(2)
        setCornerRadius(8f)
    }

    var isNavigatingBack by remember { mutableStateOf(false) }

    // Trong Composable
    val coinTotal by mainDataViewModel.coinViewModel.coinTotal.collectAsState()

    Scaffold(
        bottomBar = {
            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(8.dp))
                    .background(MaterialTheme.colorScheme.background)
                    .imePadding()
                    .navigationBarsPadding()
                    .padding(bottom = 12.dp)
            ) {
                if (true) {
                    SuggestionRow(
                        suggestions = mode.listSuggestions,
                        onSuggestionClick = { prompt ->
                            suggestionEvent = SuggestionEvent(prompt)
                            showSuggestion = false
                        }
                    )
                }
                BottomBarFreeTrialInfo()
                AppDivider()
                BottomBarInput(
                    backgroundInput = AppColors.current.backgroundInputChatBotCompose, //Color
                    background = Color.Transparent,
                    isLoadingData = mainDataViewModel.isLoadingData,
                    suggestionEvent = suggestionEvent,
                    onSend = { prompt, _, imageBitmap ->
                        if (mainDataViewModel.isLoadingData) {
                            MixedUtils.showToast(R.string.txtid_loading)
//                                    viewModel.stopListening()
                            return@BottomBarInput
                        }
                        if (prompt.isEmpty() && bitmap == null) {
                            return@BottomBarInput
                        }
                        mainDataViewModel.onEvent(
                            context,
                            ChatUiEvent.SendPrompt(
                                prompt.ifEmpty { context.getString(mode.promptImageId) },
                                imageBitmap
                            )
                        )
                        // Không cần cập nhật suggestionPrompt = "" vì nó sẽ được cập nhật từ MainViewModel
                    },
                )
            }
        }
    ) { innerPadding ->
        AppColumn(Modifier.fillMaxSize()) {
            AppRowCentered(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.background)
                    .height(AppSize.APPBAR_HEIGHT + innerPadding.calculateTopPadding())
                    .zIndex(200f)
                    .padding(top = innerPadding.calculateTopPadding())
            ) {
                AppIcon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_back),
                    tint = AppColors.current.text,
                    clickZone = AppSize.MIN_TOUCH_SIZE,
                ) {
                    if (!isNavigatingBack) {
                        isNavigatingBack = true
                        navigatorViewModel.navigateBack()
                    }
                }
                AppText(
                    text = stringResource(mode.titleId),
                    modifier = Modifier
                        .weight(1f),
                    fontSize = AppFontSize.TITLE,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Start,
                    color = AppColors.current.text
                )

                AppRow(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    AppGlideImage(
                        resId = R.drawable.svg_ic_coin_2,
                        modifier = Modifier.size(20.dp)
                    )
                    AppSpacer(4.dp)
                    AppText(
                        text = StringUtils.formatCoin(coinTotal),
                        fontSize = AppFontSize.BODY2,
                        fontWeight = FontWeight.W700,
                        lineHeight = 20.sp,
                        color = AppColors.current.text
                    )

                    Balloon(
                        builder = builder.apply {
                            setArrowOrientation(ArrowOrientation.TOP) // Arrow pointing up
                            setArrowPositionRules(ArrowPositionRules.ALIGN_ANCHOR) // Position arrow aligned with anchor
                            setBalloonAnimation(BalloonAnimation.FADE)
                            setDismissWhenClicked(false)
//                            setDismissWhenTouchOutside(true)
                            setBackgroundColor(AppColors.current.tooltipBackground)
                        },
                        onBalloonWindowInitialized = { balloonWindow = it },
                        onComposedAnchor = {
                            val count = PrefAssist.getInt(PrefConst.SHOW_TOOLTIP_TAB_CHAT_COUNTER)
                            if (count < PrefConst.SHOW_TOOLTIP_TAB_CHAT_MAX) {
                                if (count % 3 == 0)
                                    coroutineScope.launch {
                                        delay(300) // Delay 300ms để layout ổn định sau quảng cáo
                                        balloonWindow?.showAlignBottom()
                                    }
                                PrefAssist.setInt(PrefConst.SHOW_TOOLTIP_TAB_CHAT_COUNTER, count + 1)

                            }
                        }, // Show below the anchor
                        balloonContent = {
                            ModelTooltip() {
                                balloonWindow?.dismiss()
                                PrefAssist.setInt(
                                    PrefConst.SHOW_TOOLTIP_TAB_CHAT_COUNTER,
                                    PrefConst.SHOW_TOOLTIP_TAB_CHAT_MAX
                                )
                            }
                        },
                    ) {
                        AppBoxCentered(
                            modifier = Modifier
                                .size(48.dp)
                                .clip(CircleShape)
                                .appClickable {
                                    showBottomSheetSelectModel.value = true
                                },
                        ) {
                            Image(
                                painter = painterResource(selectedModelIcon),
                                modifier = Modifier
                                    .size(24.dp)
                                    .clip(RoundedCornerShape(50)),
                                contentDescription = null,
                            )
                        }
                    }
                }
            }
            ModelAiModeBottomSheet(
                showIt = showBottomSheetSelectModel.value,
                onDismissRequest = { selected ->
                    selected?.let {
                        selectedModelIcon = it.iconRes
                    }
                    showBottomSheetSelectModel.value = false
                }
            )
            AppColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.background)
                    .padding(bottom = innerPadding.calculateBottomPadding())
            ) {
                if (chatState.chatList.isEmpty()) {
                    if (showLoading.value) {
                        AppColumnCentered(
                            Modifier
                                .weight(1f)
                                .fillMaxWidth()
                                .background(
                                    color = MaterialTheme.colorScheme.background
                                ),
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.width(48.dp),
                                color = MaterialTheme.colorScheme.secondary,
                                trackColor = MaterialTheme.colorScheme.surfaceVariant,
                            )
                        }
                    } else {
                        showSuggestion = true
                        AppColumnCentered(
                            Modifier
                                .weight(1f)
                                .fillMaxWidth(),
                        ) {
                            Image(
                                modifier = Modifier.fillMaxWidth(0.6f),
                                painter = painterResource(R.drawable.svg_bg_no_chat),
                                contentDescription = null,
                            )
                            AppSpacer(32.dp)
                            AppText(
                                text = stringResource(R.string.ask_anything_get_yout_answer),
                                fontSize = AppFontSize.BODY1,
                                color = AppColors.current.text,
                                lineHeight = 24.sp,
                                fontWeight = FontWeight.W400,
                            )
                        }
//                        ChatTabDefault()
                    }

                } else {
                    showSuggestion = false
                    AppBox(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxSize()
                            .padding(horizontal = 8.dp)
                            .padding(top = 8.dp)
                    ) {
                        AndroidView(
                            factory = {
                                recyclerView.adapter = messageAdapter
                                val layoutManager = LinearLayoutManager(context).apply {
                                    stackFromEnd = true
                                }
                                recyclerView.layoutManager = layoutManager
                                recyclerView.itemAnimator = null
                                recyclerView.setItemViewCacheSize(100)

                                recyclerView
                            },
                            modifier = Modifier.fillMaxSize()
                        )
                        if (showLoading.value) {
                            AppColumnCentered(
                                Modifier
                                    .fillMaxSize()
                                    .background(
                                        color = MaterialTheme.colorScheme.background
                                    ),
                            ) {
                                CircularProgressIndicator(
                                    modifier = Modifier.width(48.dp),
                                    color = MaterialTheme.colorScheme.secondary,
                                    trackColor = MaterialTheme.colorScheme.surfaceVariant,
                                )
                            }
                            coroutineScope.launch {
                                delay(1000)
                                showLoading.value = false
                            }
                        }
                    }
                }
            }
        }
    }

}


