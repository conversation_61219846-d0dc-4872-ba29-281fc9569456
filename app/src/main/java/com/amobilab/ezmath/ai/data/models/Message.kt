package com.amobilab.ezmath.ai.data.models

import android.graphics.Bitmap

class Message(
    var image: Bitmap? = null,
    var message: String,
    var isError: <PERSON><PERSON><PERSON>,
    var sentBy: String,
    var isLike: Int? = null // null: ch<PERSON>a <PERSON> gi<PERSON>, 1: like, 0: dislike
) {
    companion object {
        var SENT_BY_ME = "me"
        var SENT_BY_ME_IMG = "me_img"
        var SENT_BY_BOT = "bot"
        var SENT_BY_BOT_GPT = "bot_gpt"
        var SENT_BY_BOT_GEMINI = "bot_gemini"
    }
}