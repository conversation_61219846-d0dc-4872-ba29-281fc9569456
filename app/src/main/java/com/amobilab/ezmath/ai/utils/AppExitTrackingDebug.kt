package com.amobilab.ezmath.ai.utils

import amobi.module.common.utils.dlog

/**
 * Debug utility để test app exit tracking
 */
object AppExitTrackingDebug {
    
    /**
     * Log current screen info
     */
    fun logCurrentScreenInfo() {
        val currentScreen = ScreenTracker.getCurrentScreenForTracking()
        dlog("Current Screen for Tracking: $currentScreen")
    }
    
    /**
     * Test screen tracking với các scenarios khác nhau
     */
    fun testScreenTracking() {
        dlog("=== Testing Screen Tracking ===")
        
        // Test HomeScreen với các tabs
        val tabs = listOf("AiChatTab", "ScanTab", "HistoryTab", "SettingTab")
        tabs.forEach { tab ->
            ScreenTracker.setCurrentScreen("HomeScreen")
            ScreenTracker.setCurrentTab(tab)
            val result = ScreenTracker.getCurrentScreenForTracking()
            dlog("HomeScreen + $tab = $result")
        }
        
        // Test các screens khác
        val screens = listOf("CoinHistory", "ChatScreen", "OnboardingScreen", "ScanDetail", "Calculator")
        screens.forEach { screen ->
            ScreenTracker.setCurrentScreen(screen)
            val result = ScreenTracker.getCurrentScreenForTracking()
            dlog("$screen = $result")
        }
    }
    
    /**
     * Simulate home button press
     */
    fun simulateHomeButtonPress() {
        val currentScreen = ScreenTracker.getCurrentScreenForTracking()
        dlog("SIMULATING HOME BUTTON PRESS - Screen: $currentScreen")
        // Không gọi FirebaseAssist để tránh spam events trong debug
    }
    
    /**
     * Simulate recent button press
     */
    fun simulateRecentButtonPress() {
        val currentScreen = ScreenTracker.getCurrentScreenForTracking()
        dlog("SIMULATING RECENT BUTTON PRESS - Screen: $currentScreen")
        // Không gọi FirebaseAssist để tránh spam events trong debug
    }
}
