package com.amobilab.ezmath.ai.utils

import amobi.module.common.utils.dlog

/**
 * Debug utility để test app exit tracking với WindowFocus method
 */
object AppExitTrackingDebug {

    /**
     * Log current screen info
     */
    fun logCurrentScreenInfo() {
        val currentScreen = ScreenTracker.getCurrentScreenForTracking()
        dlog("Current Screen for Tracking: $currentScreen")
    }

    /**
     * Test screen tracking với các scenarios khác nhau
     */
    fun testScreenTracking() {
        dlog("=== Testing Screen Tracking (WindowFocus Method) ===")

        // Test HomeScreen với các tabs
        val tabs = listOf("AiChatTab", "ScanTab", "HistoryTab", "SettingTab")
        tabs.forEach { tab ->
            ScreenTracker.setCurrentScreen("HomeScreen")
            ScreenTracker.setCurrentTab(tab)
            val result = ScreenTracker.getCurrentScreenForTracking()
            dlog("HomeScreen + $tab = $result")
        }

        // Test các screens khác
        val screens = listOf("CoinHistory", "ChatScreen", "OnboardingScreen", "ScanDetail", "Calculator")
        screens.forEach { screen ->
            ScreenTracker.setCurrentScreen(screen)
            val result = ScreenTracker.getCurrentScreenForTracking()
            dlog("$screen = $result")
        }

        dlog("WindowFocus method should work on ALL screens now!")
    }

    /**
     * Explain new detection method
     */
    fun explainNewMethod() {
        dlog("=== Simple Detection Method: onStop ===")
        dlog("HOME BUTTON: onUserLeaveHint() - Works on all screens")
        dlog("RECENT BUTTON: onStop() - Simple and should work everywhere")
        dlog("Logic: If onStop() called and NOT home button -> Recent button")
        dlog("This is the simplest approach possible")
    }
}
