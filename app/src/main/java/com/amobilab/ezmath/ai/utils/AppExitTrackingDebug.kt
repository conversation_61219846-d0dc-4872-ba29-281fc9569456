package com.amobilab.ezmath.ai.utils

import amobi.module.common.utils.dlog

/**
 * Debug utility để test app exit tracking với WindowFocus method
 */
object AppExitTrackingDebug {

    /**
     * Log current screen info
     */
    fun logCurrentScreenInfo() {
        val currentScreen = ScreenTracker.getCurrentScreenForTracking()
        dlog("Current Screen for Tracking: $currentScreen")
    }

    /**
     * Test screen tracking với các scenarios khác nhau
     */
    fun testScreenTracking() {
        dlog("=== Testing Screen Tracking (WindowFocus Method) ===")

        // Test HomeScreen với các tabs
        val tabs = listOf("AiChatTab", "ScanTab", "HistoryTab", "SettingTab")
        tabs.forEach { tab ->
            ScreenTracker.setCurrentScreen("HomeScreen")
            ScreenTracker.setCurrentTab(tab)
            val result = ScreenTracker.getCurrentScreenForTracking()
            dlog("HomeScreen + $tab = $result")
        }

        // Test các screens khác
        val screens = listOf("CoinHistory", "ChatScreen", "OnboardingScreen", "ScanDetail", "Calculator")
        screens.forEach { screen ->
            ScreenTracker.setCurrentScreen(screen)
            val result = ScreenTracker.getCurrentScreenForTracking()
            dlog("$screen = $result")
        }

        dlog("WindowFocus method should work on ALL screens now!")
    }

    /**
     * Explain new detection method
     */
    fun explainNewMethod() {
        dlog("=== New Detection Method: onWindowFocusChanged ===")
        dlog("HOME BUTTON: onUserLeaveHint() - Works on all screens")
        dlog("RECENT BUTTON: onWindowFocusChanged() - Should work on all screens")
        dlog("This method detects when app loses window focus")
        dlog("More reliable than onPause() for recent button detection")
    }
}
